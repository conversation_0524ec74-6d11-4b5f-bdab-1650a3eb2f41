// Helpers
const qs = (s,root=document)=>root.querySelector(s);
const qsa = (s,root=document)=>[...root.querySelectorAll(s)];

// Year in footer
qs('#year').textContent = new Date().getFullYear();

// Sticky navbar effect on scroll
const nav = qs('#mainNav');
let ticking = false;
const onScroll = ()=>{
  if(!ticking){
    requestAnimationFrame(()=>{
      if(window.scrollY>50) nav.classList.add('scrolled');
      else nav.classList.remove('scrolled');
      ticking = false;
    });
    ticking = true;
  }
};
window.addEventListener('scroll', onScroll);
onScroll();

// Reveal on scroll with staggered animation
const revealEls = qsa('.reveal');
const io = new IntersectionObserver((entries)=>{
  entries.forEach((e, index)=>{
    if(e.isIntersecting){
      setTimeout(()=>{
        e.target.classList.add('visible');
      }, index * 100);
      io.unobserve(e.target);
    }
  });
},{threshold:.1, rootMargin: '0px 0px -50px 0px'});
revealEls.forEach(el=>io.observe(el));

// Generate portfolio cards with placeholder images
(function buildPortfolio(){
  const template = qs('#portfolio-card-template');
  if(!template) return;
  const urls = [
    'https://images.unsplash.com/photo-1529336953121-ad5a0d43d0d2?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1557800636-894a64c1696f?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1518770660439-4636190af475?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1498050108023-c5249f4df085?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1487014679447-9f8336841d58?auto=format&fit=crop&w=800&q=60'
  ];
  const row = template.parentElement;
  urls.forEach(u=>{
    const frag = template.content.cloneNode(true);
    const img = qs('img', frag);
    img.src = u; img.alt = 'عمل من أعمالنا';
    row.appendChild(frag);
  });
})();

// Smooth scroll for nav links
qsa('a.nav-link, .navbar .btn, a.btn').forEach(a=>{
  a.addEventListener('click', (e)=>{
    const href = a.getAttribute('href');
    if(href && href.startsWith('#') && href.length>1){
      const target = qs(href);
      if(target){
        e.preventDefault();
        const offsetTop = target.offsetTop - 80;
        window.scrollTo({top: offsetTop, behavior:'smooth'});

        // Close mobile menu if open
        const navCollapse = qs('.navbar-collapse');
        if(navCollapse && navCollapse.classList.contains('show')){
          bootstrap.Collapse.getInstance(navCollapse)?.hide();
        }
      }
    }
  });
});

// Form submission handler
const contactForm = qs('footer form');
if(contactForm){
  contactForm.addEventListener('submit', (e)=>{
    e.preventDefault();
    const btn = qs('button[type="submit"]', contactForm);
    const originalText = btn.textContent;

    btn.textContent = 'جاري الإرسال...';
    btn.disabled = true;

    // Simulate form submission
    setTimeout(()=>{
      btn.textContent = 'تم الإرسال ✓';
      btn.style.background = 'linear-gradient(90deg, #28a745, #20c997)';

      setTimeout(()=>{
        btn.textContent = originalText;
        btn.disabled = false;
        btn.style.background = '';
        contactForm.reset();
      }, 2000);
    }, 1500);
  });
}

// Add loading states and error handling for images
qsa('img').forEach(img=>{
  img.addEventListener('load', ()=>{
    img.style.opacity = '1';
  });
  img.addEventListener('error', ()=>{
    img.style.opacity = '0.5';
    img.alt = 'فشل تحميل الصورة';
  });
});

// Parallax effect for hero background
window.addEventListener('scroll', ()=>{
  const scrolled = window.pageYOffset;
  const heroBackground = qs('.hero-bg');
  if(heroBackground && scrolled < window.innerHeight){
    heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
  }
});

// Counter animation for stats
const animateCounters = ()=>{
  const counters = qsa('.stat-number[data-count]');
  counters.forEach(counter=>{
    const target = parseInt(counter.getAttribute('data-count'));
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;

    const updateCounter = ()=>{
      current += increment;
      if(current < target){
        counter.textContent = Math.floor(current);
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target;
      }
    };
    updateCounter();
  });
};

// Trigger counter animation when stats section is visible
const statsSection = qs('#stats, .stat-item');
if(statsSection){
  const statsObserver = new IntersectionObserver((entries)=>{
    entries.forEach(entry=>{
      if(entry.isIntersecting){
        animateCounters();
        statsObserver.unobserve(entry.target);
      }
    });
  }, {threshold: 0.5});

  const statsContainer = qs('.stat-item').closest('section') || qs('.stat-item').closest('.container-xxl');
  if(statsContainer) statsObserver.observe(statsContainer);
}

// Service card interactions
qsa('.service-card').forEach(card=>{
  card.addEventListener('mouseenter', ()=>{
    card.style.transform = 'translateY(-10px) scale(1.02)';
  });
  card.addEventListener('mouseleave', ()=>{
    card.style.transform = '';
  });
});

// Pricing card selection
qsa('.pricing-card .btn').forEach(btn=>{
  btn.addEventListener('click', (e)=>{
    e.preventDefault();
    const card = btn.closest('.pricing-card');
    const title = qs('.card-title', card).textContent;

    // Scroll to contact form
    const contactForm = qs('#contact');
    if(contactForm){
      contactForm.scrollIntoView({behavior: 'smooth'});

      // Pre-fill form with selected package
      setTimeout(()=>{
        const messageField = qs('textarea', contactForm);
        if(messageField){
          messageField.value = `أرغب في الاستفسار عن ${title}`;
          messageField.focus();
        }
      }, 1000);
    }
  });
});

// Blog card hover effects
qsa('.blog-card').forEach(card=>{
  card.addEventListener('mouseenter', ()=>{
    const img = qs('img', card);
    if(img) img.style.transform = 'scale(1.1)';
  });
  card.addEventListener('mouseleave', ()=>{
    const img = qs('img', card);
    if(img) img.style.transform = '';
  });
});

// Team card social links
qsa('.team-card').forEach(card=>{
  const socialLinks = qsa('.social-links a', card);
  socialLinks.forEach(link=>{
    link.addEventListener('click', (e)=>{
      e.preventDefault();
      // Add your social media links here
      console.log('Social link clicked:', link.getAttribute('aria-label'));
    });
  });
});

// Enhanced form validation
const enhanceForm = ()=>{
  const form = qs('footer form');
  if(!form) return;

  const inputs = qsa('input, textarea', form);
  inputs.forEach(input=>{
    input.addEventListener('blur', ()=>{
      if(input.hasAttribute('required') && !input.value.trim()){
        input.style.borderColor = '#dc3545';
        input.style.boxShadow = '0 0 0 0.2rem rgba(220,53,69,.25)';
      } else {
        input.style.borderColor = '';
        input.style.boxShadow = '';
      }
    });

    input.addEventListener('input', ()=>{
      if(input.style.borderColor === 'rgb(220, 53, 69)'){
        input.style.borderColor = '';
        input.style.boxShadow = '';
      }
    });
  });
};

enhanceForm();

// Add loading animation to buttons
qsa('.btn').forEach(btn=>{
  btn.addEventListener('click', function(e){
    if(!this.classList.contains('loading')){
      this.classList.add('loading');
      setTimeout(()=>{
        this.classList.remove('loading');
      }, 2000);
    }
  });
});

