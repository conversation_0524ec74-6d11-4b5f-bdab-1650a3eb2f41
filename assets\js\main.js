// Helpers
const qs = (s,root=document)=>root.querySelector(s);
const qsa = (s,root=document)=>[...root.querySelectorAll(s)];

// Year in footer
qs('#year').textContent = new Date().getFullYear();

// Sticky navbar effect on scroll
const nav = qs('#mainNav');
let ticking = false;
const onScroll = ()=>{
  if(!ticking){
    requestAnimationFrame(()=>{
      if(window.scrollY>50) nav.classList.add('scrolled');
      else nav.classList.remove('scrolled');
      ticking = false;
    });
    ticking = true;
  }
};
window.addEventListener('scroll', onScroll);
onScroll();

// Reveal on scroll with staggered animation
const revealEls = qsa('.reveal');
const io = new IntersectionObserver((entries)=>{
  entries.forEach((e, index)=>{
    if(e.isIntersecting){
      setTimeout(()=>{
        e.target.classList.add('visible');
      }, index * 100);
      io.unobserve(e.target);
    }
  });
},{threshold:.1, rootMargin: '0px 0px -50px 0px'});
revealEls.forEach(el=>io.observe(el));

// Generate portfolio cards with placeholder images
(function buildPortfolio(){
  const template = qs('#portfolio-card-template');
  if(!template) return;
  const urls = [
    'https://images.unsplash.com/photo-1529336953121-ad5a0d43d0d2?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1557800636-894a64c1696f?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1518770660439-4636190af475?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1498050108023-c5249f4df085?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1487014679447-9f8336841d58?auto=format&fit=crop&w=800&q=60'
  ];
  const row = template.parentElement;
  urls.forEach(u=>{
    const frag = template.content.cloneNode(true);
    const img = qs('img', frag);
    img.src = u; img.alt = 'عمل من أعمالنا';
    row.appendChild(frag);
  });
})();

// Smooth scroll for nav links
qsa('a.nav-link, .navbar .btn, a.btn').forEach(a=>{
  a.addEventListener('click', (e)=>{
    const href = a.getAttribute('href');
    if(href && href.startsWith('#') && href.length>1){
      const target = qs(href);
      if(target){
        e.preventDefault();
        const offsetTop = target.offsetTop - 80;
        window.scrollTo({top: offsetTop, behavior:'smooth'});

        // Close mobile menu if open
        const navCollapse = qs('.navbar-collapse');
        if(navCollapse && navCollapse.classList.contains('show')){
          bootstrap.Collapse.getInstance(navCollapse)?.hide();
        }
      }
    }
  });
});

// Form submission handler
const contactForm = qs('footer form');
if(contactForm){
  contactForm.addEventListener('submit', (e)=>{
    e.preventDefault();
    const btn = qs('button[type="submit"]', contactForm);
    const originalText = btn.textContent;

    btn.textContent = 'جاري الإرسال...';
    btn.disabled = true;

    // Simulate form submission
    setTimeout(()=>{
      btn.textContent = 'تم الإرسال ✓';
      btn.style.background = 'linear-gradient(90deg, #28a745, #20c997)';

      setTimeout(()=>{
        btn.textContent = originalText;
        btn.disabled = false;
        btn.style.background = '';
        contactForm.reset();
      }, 2000);
    }, 1500);
  });
}

// Add loading states and error handling for images
qsa('img').forEach(img=>{
  img.addEventListener('load', ()=>{
    img.style.opacity = '1';
  });
  img.addEventListener('error', ()=>{
    img.style.opacity = '0.5';
    img.alt = 'فشل تحميل الصورة';
  });
});

// Parallax effect for hero background
window.addEventListener('scroll', ()=>{
  const scrolled = window.pageYOffset;
  const heroBackground = qs('.hero-bg');
  if(heroBackground && scrolled < window.innerHeight){
    heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
  }
});

