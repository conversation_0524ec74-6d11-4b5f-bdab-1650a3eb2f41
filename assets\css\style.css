/* Base theme */
:root{
  --bg:#0b1220;
  --bg-2:#0e1626;
  --primary:#4ea9ff;
  --primary-2:#7a5cff;
  --text:#e9f0ff;
  --muted:#9fb1d0;
  --card:rgba(255,255,255,.08);
  --border:rgba(255,255,255,.12);
  --grad: radial-gradient(1200px 600px at 20% -10%, rgba(126,77,255,.3), transparent 50%),
          radial-gradient(1000px 600px at 80% 10%, rgba(30,144,255,.25), transparent 60%),
          linear-gradient(180deg, #0b1220 0%, #0a1020 100%);
}

*{box-sizing:border-box;}
html{scroll-behavior:smooth;}
html,body{height:100%;}
body{
  background: var(--grad);
  background-attachment: fixed;
  color: var(--text);
  font-family: "Tajawal", system-ui, -apple-system, <PERSON><PERSON><PERSON> UI, <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Not<PERSON>", sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

.section-pad{padding: 6rem 0;}

/* Navbar */
.navbar{
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.navbar.scrolled{
  background: rgba(11,18,32,.85)!important;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--border);
  box-shadow: 0 2px 20px rgba(0,0,0,.3);
}
.navbar .navbar-brand{
  letter-spacing:.5px;
  font-weight: 800;
  font-size: 1.5rem;
}
.navbar .nav-link{
  font-weight: 500;
  transition: color 0.3s ease;
}
.navbar .nav-link:hover{
  color: var(--primary)!important;
}
.navbar .btn-primary{
  background: linear-gradient(90deg, var(--primary), var(--primary-2));
  border:0;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}
.navbar .btn-primary:hover{
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(78,169,255,.4);
}

/* Hero */
#hero{
  padding-top: 8rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
}
.hero-bg{
  position:absolute;
  inset:0;
  background:
    radial-gradient(800px 400px at 70% 20%, rgba(78,169,255,.25), transparent 40%),
    radial-gradient(600px 300px at 20% 40%, rgba(122,92,255,.2), transparent 50%),
    radial-gradient(400px 200px at 90% 80%, rgba(255,107,107,.15), transparent 60%);
  filter: blur(60px);
  opacity:.8;
  animation: float 20s ease-in-out infinite;
}
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}
.text-gradient{
  background-image: linear-gradient(135deg, var(--primary), var(--primary-2), #ff6b6b);
  -webkit-background-clip:text;
  background-clip:text;
  color: transparent;
  font-weight: 800;
}
.hero-figure{
  position: relative;
}
.hero-figure::before{
  content: '';
  position: absolute;
  inset: -20px;
  background: linear-gradient(45deg, var(--primary), var(--primary-2));
  border-radius: 2rem;
  opacity: 0.1;
  filter: blur(20px);
  z-index: -1;
}
.hero-figure img{
  border:1px solid var(--border);
  transition: transform 0.5s ease;
}
.hero-figure:hover img{
  transform: scale(1.02);
}

/* Cards */
.glass{
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0,0,0,.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.glass::before{
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,.2), transparent);
}
.glass:hover{
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,.3);
  border-color: rgba(78,169,255,.3);
}
.card .icon{
  font-size:2.5rem;
  color: var(--primary);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}
.card .icon.small{
  font-size:2rem;
}
.glass:hover .icon{
  color: var(--primary-2);
  transform: scale(1.1);
}
.card-title{
  font-weight: 700;
  margin-bottom: 1rem;
}
.card-text{
  line-height: 1.7;
}

/* Portfolio */
.portfolio-card{
  position:relative;
  border-radius: 1rem;
  overflow:hidden;
  background:var(--card);
  border:1px solid var(--border);
  transition: all 0.4s ease;
  cursor: pointer;
}
.portfolio-card::after{
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(78,169,255,.1), rgba(122,92,255,.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}
.portfolio-card:hover::after{
  opacity: 1;
}
.portfolio-card img{
  display:block;
  aspect-ratio: 4/3;
  object-fit: cover;
  transition: transform .6s ease;
  width: 100%;
}
.portfolio-card:hover{
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0,0,0,.4);
  border-color: rgba(78,169,255,.4);
}
.portfolio-card:hover img{
  transform: scale(1.08);
}

/* Footer */
footer .form-control{
  background: rgba(255,255,255,.08);
  border: 1px solid var(--border);
  color: var(--text);
  transition: all 0.3s ease;
  font-weight: 500;
}
footer .form-control:focus{
  background: rgba(255,255,255,.12);
  border-color: var(--primary);
  color: var(--text);
  box-shadow: 0 0 0 0.2rem rgba(78,169,255,.25);
}
footer .form-control::placeholder{
  color: var(--muted);
}
footer .btn-primary{
  background: linear-gradient(90deg, var(--primary), var(--primary-2));
  border:0;
  font-weight: 600;
  transition: all 0.3s ease;
}
footer .btn-primary:hover{
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(78,169,255,.4);
}

/* Reveal on scroll */
.reveal{
  opacity:0;
  transform: translateY(30px);
  transition: all .8s cubic-bezier(0.4, 0, 0.2, 1);
}
.reveal.visible{
  opacity:1;
  transform:none;
}

/* Utilities */
hr{border-color: var(--border);}
.btn{
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}
.btn-outline-light:hover{
  background: rgba(255,255,255,.1);
  border-color: rgba(255,255,255,.3);
}

/* Responsive tweaks */
@media (max-width: 992px){
  .section-pad{padding: 4rem 0}
  #hero{padding-top: 7rem; min-height: 90vh;}
  .hero-bg{filter: blur(40px);}
  .display-6{font-size: 2rem;}
}

@media (max-width: 768px){
  .section-pad{padding: 3rem 0}
  #hero{padding-top: 6rem; min-height: 80vh;}
  .navbar .btn{font-size: 0.9rem; padding: 0.4rem 1rem;}
  .hero-figure{margin-top: 2rem;}
}

@media (max-width: 576px){
  .section-pad{padding: 2.5rem 0}
  #hero{padding-top: 5rem;}
  .display-5{font-size: 2rem;}
  .lead{font-size: 1rem;}
}

/* New Sections Styles */

/* Hero Enhancements */
.hero-video-container{
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
}
.hero-overlay{
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(78,169,255,.1), rgba(122,92,255,.1));
  display: flex;
  align-items: center;
  justify-content: center;
}
.floating-elements{
  position: relative;
  width: 100%;
  height: 100%;
}
.floating-icon{
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255,255,255,.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--primary);
  animation: float-icon 6s ease-in-out infinite;
  animation-delay: var(--delay);
}
.floating-icon:nth-child(1){top: 20%; left: 20%;}
.floating-icon:nth-child(2){top: 60%; right: 20%;}
.floating-icon:nth-child(3){bottom: 30%; left: 30%;}
.floating-icon:nth-child(4){top: 40%; right: 40%;}

@keyframes float-icon {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

/* Service Cards */
.service-card{
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}
.service-card:hover{
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0,0,0,.4);
}
.service-features .badge{
  font-size: 0.7rem;
  padding: 0.3rem 0.6rem;
  border-radius: 1rem;
}

/* Solutions Section */
.bg-dark-gradient{
  background: linear-gradient(135deg, rgba(11,18,32,.95), rgba(14,22,38,.95));
  position: relative;
}
.bg-dark-gradient::before{
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 30% 50%, rgba(78,169,255,.05), transparent 50%);
}
.feature-icon{
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--primary), var(--primary-2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  flex-shrink: 0;
}
.solutions-grid{
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}
.solution-card{
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}
.solution-card:hover{
  transform: translateY(-5px);
  border-color: var(--primary);
}
.solution-icon{
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, var(--primary), var(--primary-2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin: 0 auto 1rem;
}

/* Stats */
.stat-item{
  padding: 1rem;
}
.stat-number{
  transition: all 0.3s ease;
}

/* Team Cards */
.team-card{
  transition: all 0.4s ease;
}
.team-card:hover{
  transform: translateY(-8px);
}
.team-avatar{
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 50%;
  border: 3px solid var(--border);
}
.team-avatar img{
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.team-overlay{
  position: absolute;
  inset: 0;
  background: rgba(78,169,255,.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.team-card:hover .team-overlay{
  opacity: 1;
}
.team-card:hover .team-avatar img{
  transform: scale(1.1);
}
.social-links{
  display: flex;
  gap: 1rem;
}
.social-links a{
  width: 35px;
  height: 35px;
  background: rgba(255,255,255,.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.social-links a:hover{
  background: white;
  color: var(--primary)!important;
  transform: scale(1.1);
}

/* Pricing Cards */
.pricing-card{
  transition: all 0.4s ease;
  position: relative;
}
.pricing-card:hover{
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,.3);
}
.pricing-card.featured{
  border: 2px solid var(--primary);
  transform: scale(1.05);
}
.pricing-card.featured:hover{
  transform: scale(1.05) translateY(-10px);
}
.featured-badge{
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, var(--primary), var(--primary-2));
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
}
.pricing-icon{
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, var(--primary), var(--primary-2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  margin: 0 auto;
}
.pricing-amount{
  position: relative;
}

/* Testimonials */
.testimonial-card{
  transition: all 0.3s ease;
}
.testimonial-card:hover{
  transform: translateY(-5px);
}
.testimonial-stars{
  font-size: 1.1rem;
}
.testimonial-author img{
  border: 2px solid var(--border);
}

/* Blog Cards */
.blog-card{
  transition: all 0.4s ease;
  overflow: hidden;
}
.blog-card:hover{
  transform: translateY(-8px);
}
.blog-image{
  position: relative;
  overflow: hidden;
}
.blog-image img{
  height: 200px;
  object-fit: cover;
  transition: transform 0.4s ease;
}
.blog-card:hover .blog-image img{
  transform: scale(1.1);
}
.blog-category{
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--primary);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}
.blog-meta{
  font-size: 0.85rem;
}

/* Additional Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.pulse{
  animation: pulse 2s infinite;
}

/* Enhanced Responsive */
@media (max-width: 992px){
  .solutions-grid{
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  .floating-icon{
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }
  .team-avatar{
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 768px){
  .hero-overlay{
    display: none;
  }
  .pricing-card.featured{
    transform: none;
  }
  .pricing-card.featured:hover{
    transform: translateY(-5px);
  }
  .stat-number{
    font-size: 2.5rem!important;
  }
}

@media (max-width: 576px){
  .solution-card{
    padding: 1rem;
  }
  .solution-icon{
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  .team-avatar{
    width: 80px;
    height: 80px;
  }
  .pricing-icon{
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

